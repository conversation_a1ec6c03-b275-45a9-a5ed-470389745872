import React, { useState } from "react";
import TextArea from "../components/TextArea";
import MessageDisplay from "../components/MessageDisplay";
import { FaT<PERSON>, FaClipboardList, FaReply } from "react-icons/fa";
import "./css/FormDVCLT.css";
import { API_BASE_URL, TOKEN_CONFIG } from "../config";
import Snackbar from "../components/Snackbar";
import axios from "axios";
import { callNGSPAPI, fetchNGSPToken as fetchNGSPTokenHelper, openNGSPCertificateAcceptance } from "../utils/httpClient";

interface LogResult {
  id: string;
  api: string;
  responseBody: any;
  requestBody: any;
  nationCode?: string;
}

const FormDVCLT: React.FC = () => {
  const [inputText, setInputText] = useState<string>("");
  const [option, setOption] = useState<string>("Cập nhật trạng thái sang DVCLT");
  const [module, setModule] = useState<string>("LTKT");
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [logResults, setLogResults] = useState<LogResult[]>([]);
  const [snackbarVisible, setSnackbarVisible] = useState<boolean>(false);
  const [selectedStatus, setSelectedStatus] = useState<{ [key: number]: number }>({});
  const [selectedNote, setSelectedNote] = useState<{ [key: number]: string }>({});

  // New states for judicial civil status sync
  const [judicialData, setJudicialData] = useState<any[]>([]);
  const [selectedCivilStatusType, setSelectedCivilStatusType] = useState<{ [key: number]: string }>({});
  const [showCivilStatusOptions, setShowCivilStatusOptions] = useState<boolean>(false);
  const [judicialResponses, setJudicialResponses] = useState<{ [key: number]: any }>({});
  const [enableDirectCall, setEnableDirectCall] = useState<{ [key: number]: boolean }>({});
  const [selectedGateway, setSelectedGateway] = useState<{ [key: number]: string }>({});

  // States for editing response before gateway call
  const [editableResponses, setEditableResponses] = useState<{ [key: number]: string }>({});
  const [isEditingResponse, setIsEditingResponse] = useState<{ [key: number]: boolean }>({});

  // States for editing resource data before sync
  const [editableResources, setEditableResources] = useState<{ [key: number]: string }>({});
  const [isEditingResource, setIsEditingResource] = useState<{ [key: number]: boolean }>({});

  // States for direct DVCLT call
  const [enableDirectDVCLT, setEnableDirectDVCLT] = useState<{ [key: number]: boolean }>({});

  // New states for sync status feature
  const [syncStatusData, setSyncStatusData] = useState<any[]>([]);
  const [showSyncStatusOptions, setShowSyncStatusOptions] = useState<boolean>(false);
  const [syncStatusResponses, setSyncStatusResponses] = useState<{ [key: number]: any }>({});
  const [enableDirectStatusCall, setEnableDirectStatusCall] = useState<{ [key: number]: boolean }>({});
  const [selectedStatusGateway, setSelectedStatusGateway] = useState<{ [key: number]: string }>({});
  const [noiDangKyInput, setNoiDangKyInput] = useState<{ [key: number]: string }>({});

  // Network/VPN related states
  const [showNetworkWarning, setShowNetworkWarning] = useState<boolean>(false);
  const [networkErrorMessage, setNetworkErrorMessage] = useState<string>("");

  // Loading states for individual buttons
  const [resendLoading, setResendLoading] = useState<boolean[]>([]);
  const [directCallLoading, setDirectCallLoading] = useState<boolean[]>([]);
  const [statusCallLoading, setStatusCallLoading] = useState<boolean[]>([]);
  const [syncLoading, setSyncLoading] = useState<boolean[]>([]);

  // Modal state for response display
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [modalResponse, setModalResponse] = useState<any>(null);
  const [modalTitle, setModalTitle] = useState("");

  // States for code conversion feature
  const [codeConversionData, setCodeConversionData] = useState<any[]>([]);
  const [showCodeConversionOptions, setShowCodeConversionOptions] = useState<boolean>(false);
  const [globalConversionType, setGlobalConversionType] = useState<string>("");
  const [conversionResults, setConversionResults] = useState<string[]>([]);
  const [conversionLoading, setConversionLoading] = useState<boolean>(false);
  const [judicialResendLoading, setJudicialResendLoading] = useState<boolean>(false);
  const [fetchAndSyncLoading, setFetchAndSyncLoading] = useState<boolean>(false);

  // Status options with corresponding notes
  const statusOptions = [
    { value: 1, label: "Hệ thống MCĐT đã tiếp nhận hồ sơ", note: "Hệ thống MCĐT tự động trả khi nhận được hồ sơ từ hệ thống DVCLT" },
    { value: 2, label: "Hồ sơ cần bổ sung thông tin", note: "Hồ sơ cần bổ sung thông tin" },
    { value: 3, label: "Hồ sơ đủ điều kiện giải quyết", note: "Hồ sơ đủ điều kiện giải quyết" },
    { value: 4, label: "Đã hoàn thành đăng ký", note: "Đã hoàn thành đăng ký" },
    { value: 5, label: "Đã trả kết quả", note: "Đã trả kết quả" },
    { value: 6, label: "Từ chối tiếp nhận", note: "Từ chối tiếp nhận" },
    { value: 7, label: "Đã chuyển cán bộ Tư pháp", note: "Đã chuyển cán bộ Tư pháp" },
    { value: 8, label: "Đã sửa hồ sơ", note: "Sau khi cán bộ hộ tịch tiếp nhận, sửa hồ sơ…MCĐT trả trạng thái này cho DVCLT" }
  ];

  const handleChangeText = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIsError(false);
    setInputText(event.target.value);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
  };

  // Helper functions for loading states
  const setButtonLoading = (type: 'resend' | 'directCall' | 'statusCall' | 'sync', index: number, loading: boolean) => {
    switch (type) {
      case 'resend':
        setResendLoading(prev => {
          const newState = [...prev];
          newState[index] = loading;
          return newState;
        });
        break;
      case 'directCall':
        setDirectCallLoading(prev => {
          const newState = [...prev];
          newState[index] = loading;
          return newState;
        });
        break;
      case 'statusCall':
        setStatusCallLoading(prev => {
          const newState = [...prev];
          newState[index] = loading;
          return newState;
        });
        break;
      case 'sync':
        setSyncLoading(prev => {
          const newState = [...prev];
          newState[index] = loading;
          return newState;
        });
        break;
    }
  };

  // Helper function to format JSON with unquoted array values
  const formatJSONWithUnquotedArrays = (obj: any, indent: number = 2): string => {
    const spaces = ' '.repeat(indent);

    if (Array.isArray(obj)) {
      if (obj.length === 0) return '[]';

      // Check if this is an array of strings (like convertedCodes)
      if (obj.every(item => typeof item === 'string')) {
        const items = obj.map(item => `${spaces}  ${item}`).join(',\n');
        return `[\n${items}\n${spaces}]`;
      } else {
        const items = obj.map(item => `${spaces}  ${formatJSONWithUnquotedArrays(item, indent + 2)}`).join(',\n');
        return `[\n${items}\n${spaces}]`;
      }
    }

    if (obj && typeof obj === 'object') {
      const entries = Object.entries(obj);
      if (entries.length === 0) return '{}';

      const items = entries.map(([key, value]) => {
        const formattedValue = formatJSONWithUnquotedArrays(value, indent + 2);
        return `${spaces}  "${key}": ${formattedValue}`;
      }).join(',\n');

      return `{\n${items}\n${spaces}}`;
    }

    // For primitive values
    if (typeof obj === 'string') {
      return `"${obj}"`;
    }

    return JSON.stringify(obj);
  };

  // Helper function to show response in modal
  const showResponseInModal = (title: string, response: any) => {
    setModalTitle(title);
    setModalResponse(response);
    setShowResponseModal(true);
  };

  // Helper function to close modal
  const closeModal = () => {
    setShowResponseModal(false);
    setModalResponse(null);
    setModalTitle("");
  };

  const handleChangeOption = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setOption(event.target.value);
    setIsError(false);
    setMessage("");
    setSnackbarVisible(false);
    setLogResults([]);
    setJudicialData([]);
    setShowCivilStatusOptions(false);
    setSelectedCivilStatusType({});
    setJudicialResponses({});
    setEditableResponses({});
    setIsEditingResponse({});
    setEditableResources({});
    setIsEditingResource({});
    setEnableDirectDVCLT({});
    // Reset sync status states
    setSyncStatusData([]);
    setShowSyncStatusOptions(false);
    setSyncStatusResponses({});
    setEnableDirectStatusCall({});
    setSelectedStatusGateway({});
    setNoiDangKyInput({});
    // Reset code conversion states
    setCodeConversionData([]);
    setShowCodeConversionOptions(false);
    setGlobalConversionType("");
    setConversionResults([]);
    setConversionLoading(false);
  };

  const handleChangeModule = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setModule(event.target.value);
    setIsError(false);
    setMessage("");
    setSnackbarVisible(false);
  };

  const handleStatusChange = (index: number, status: number) => {
    setSelectedStatus(prev => ({ ...prev, [index]: status }));
    // Auto-select corresponding note
    const statusOption = statusOptions.find(opt => opt.value === status);
    if (statusOption) {
      setSelectedNote(prev => ({ ...prev, [index]: statusOption.note }));
    }
  };

  const handleNoteChange = (index: number, note: string) => {
    setSelectedNote(prev => ({ ...prev, [index]: note }));
  };

  const handleCivilStatusTypeChange = (index: number, type: string) => {
    setSelectedCivilStatusType(prev => ({ ...prev, [index]: type }));
  };

  const handleDirectCallToggle = (index: number, enabled: boolean) => {
    setEnableDirectCall(prev => ({ ...prev, [index]: enabled }));
    if (enabled && !selectedGateway[index]) {
      setSelectedGateway(prev => ({ ...prev, [index]: "LGSP" }));
    }
  };

  const handleDirectDVCLTToggle = (index: number, enabled: boolean) => {
    setEnableDirectDVCLT(prev => ({ ...prev, [index]: enabled }));
  };

  const handleGatewayChange = (index: number, gateway: string) => {
    setSelectedGateway(prev => ({ ...prev, [index]: gateway }));
  };

  // Handlers for sync status feature
  const handleDirectStatusCallToggle = (index: number, enabled: boolean) => {
    setEnableDirectStatusCall(prev => ({ ...prev, [index]: enabled }));
    if (enabled && !selectedStatusGateway[index]) {
      setSelectedStatusGateway(prev => ({ ...prev, [index]: "LGSP" }));
    }
  };

  const handleStatusGatewayChange = (index: number, gateway: string) => {
    setSelectedStatusGateway(prev => ({ ...prev, [index]: gateway }));
  };

  const handleNoiDangKyChange = (index: number, value: string) => {
    setNoiDangKyInput(prev => ({ ...prev, [index]: value }));
  };

  // Helper function to handle NGSP network errors
  const handleNGSPNetworkError = (error: any): void => {
    setNetworkErrorMessage(error.message);
    setShowNetworkWarning(true);
    setMessage(`${error.message}. Bạn có thể accept SSL certificate hoặc dùng LGSP.`);
    setIsError(true);
    setSnackbarVisible(true);
    setLoading(false);
  };

  // Helper function to open NGSP in new tab for SSL acceptance
  const handleAcceptNGSPCertificate = () => {
    openNGSPCertificateAcceptance();
    setTimeout(() => {
      setMessage('Sau khi accept certificate, thử gọi API lại.');
      setIsError(false);
      setSnackbarVisible(true);
      setShowNetworkWarning(false);
    }, 1000);
  };

  // Helper function to call NGSP API with HTTPS/HTTP fallback
  const callNGSPAPILocal = async (token: string, requestBody: any): Promise<any> => {
    try {
      // Use the helper function for better SSL handling
      const response = await callNGSPAPI('/Lienthonghotich/1.0/nhanHoSoDKHT', requestBody, token);
      return response;
    } catch (error: any) {
      console.log('NGSP API call failed:', error.message);
      throw error;
    }
  };

  const handleToggleEditResponse = (index: number) => {
    if (!isEditingResponse[index]) {
      // Initialize editable response with current response
      const currentResponse = judicialResponses[index];
      setEditableResponses(prev => ({
        ...prev,
        [index]: JSON.stringify(currentResponse, null, 2)
      }));
    }
    setIsEditingResponse(prev => ({ ...prev, [index]: !prev[index] }));
  };

  const handleUpdateEditableResponse = (index: number, value: string) => {
    setEditableResponses(prev => ({ ...prev, [index]: value }));
  };

  const handleSaveEditedResponse = (index: number) => {
    try {
      const parsedResponse = JSON.parse(editableResponses[index]);
      setJudicialResponses(prev => ({ ...prev, [index]: parsedResponse }));
      setIsEditingResponse(prev => ({ ...prev, [index]: false }));
      setMessage("Response đã được cập nhật!");
      setIsError(false);
      setSnackbarVisible(true);
    } catch (error) {
      setMessage("Lỗi JSON format! Vui lòng kiểm tra lại.");
      setIsError(true);
      setSnackbarVisible(true);
    }
  };

  const handleCancelEditResponse = (index: number) => {
    setIsEditingResponse(prev => ({ ...prev, [index]: false }));
    setEditableResponses(prev => ({ ...prev, [index]: "" }));
  };

  // Handlers for editing resource data
  const handleToggleEditResource = (index: number) => {
    if (!isEditingResource[index]) {
      // Initialize editable resource with current resource data
      const currentResource = judicialData[index]?.resource;
      setEditableResources(prev => ({
        ...prev,
        [index]: JSON.stringify(currentResource, null, 2)
      }));
    }
    setIsEditingResource(prev => ({ ...prev, [index]: !prev[index] }));
  };

  const handleUpdateEditableResource = (index: number, value: string) => {
    setEditableResources(prev => ({ ...prev, [index]: value }));
  };

  const handleSaveEditedResource = (index: number) => {
    try {
      const parsedResource = JSON.parse(editableResources[index]);
      setJudicialData(prev => {
        const newData = [...prev];
        newData[index] = {
          ...newData[index],
          resource: parsedResource
        };
        return newData;
      });
      setIsEditingResource(prev => ({ ...prev, [index]: false }));
      setMessage("Dữ liệu resource đã được cập nhật!");
      setIsError(false);
      setSnackbarVisible(true);
    } catch (error) {
      setMessage("Lỗi JSON format! Vui lòng kiểm tra lại.");
      setIsError(true);
      setSnackbarVisible(true);
    }
  };

  const handleCancelEditResource = (index: number) => {
    setIsEditingResource(prev => ({ ...prev, [index]: false }));
    setEditableResources(prev => ({ ...prev, [index]: "" }));
  };

  // Utility function to create basic auth header
  const createBasicAuthHeader = (username: string, password: string): string => {
    const credentials = btoa(`${username}:${password}`);
    console.log('Creating basic auth for:', {
      username,
      credentialsLength: credentials.length,
      generatedCredentials: credentials,
      expectedFromCurl: 'WTIxZEx0aGRNS1hjQmZoRHZFcFJSZkxiMzBzYTpEOW5tSEhsZUtpeE53cmhwNEJoRk01N3VTSXdh'
    });
    return `Basic ${credentials}`;
  };

  const fetchLGSPToken = async (): Promise<string | null> => {
    try {
      const authHeader = createBasicAuthHeader(TOKEN_CONFIG.LGSP.username, TOKEN_CONFIG.LGSP.password);
      const response = await axios.post(
        `${TOKEN_CONFIG.LGSP.endpoint}?grant_type=client_credentials`,
        {},
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'grant_type': 'client_credentials',
            'Authorization': authHeader
          }
        }
      );
      return response.data.access_token;
    } catch (error) {
      console.error('Error fetching LGSP token:', error);
      return null;
    }
  };

  const fetchNGSPToken = async (): Promise<string | null> => {
    if (!TOKEN_CONFIG.NGSP.username || !TOKEN_CONFIG.NGSP.password) {
      console.error('NGSP credentials not configured properly');
      return null;
    }

    try {
      // Use the helper function for better SSL handling
      const token = await fetchNGSPTokenHelper(TOKEN_CONFIG.NGSP.username, TOKEN_CONFIG.NGSP.password);
      return token;
    } catch (error: any) {
      console.log('NGSP token fetch failed:', error.message);

      // Enhanced error handling for network issues
      if (error.code === 'ERR_NETWORK' ||
        error.message === 'Network Error' ||
        error.code === 'ERR_CERT_AUTHORITY_INVALID' ||
        error.isSSLError) {
        console.error('🔒 NGSP SSL Certificate Error:');
        console.error('Error details:', {
          code: error.code,
          message: error.message,
          stack: error.stack?.substring(0, 200)
        });
        console.error('💡 Solutions:');
        console.error('1. Navigate to https://api.ngsp.gov.vn/token and accept certificate');
        console.error('2. Chrome: chrome://flags/#allow-insecure-localhost → Enable');
        console.error('3. Chrome args: --ignore-certificate-errors --ignore-ssl-errors');
        console.error('4. Add api.ngsp.gov.vn to trusted sites');
        console.error('5. Use LGSP alternative instead');
        console.error('6. VPN may be required to access api.ngsp.gov.vn');
        throw new Error('Lỗi SSL Certificate - Cần accept certificate cho api.ngsp.gov.vn');
      }

      console.error('Error fetching NGSP token:', error);
      console.error('Error response:', error.response?.data);
      return null;
    }
  };

  const fetchDVCLTToken = async (): Promise<string | null> => {
    try {
      const response = await axios.post(
        'http://***********:8080/VXPAdapter/RestService/forward/oauth2/JWTToken/?providerurl=http://lienthong.dichvucong.gov.vn:8135&dstcode=VN:GOV:G01:CSDLDC',
        {
          secretId: "mcdt_prod",
          secretPass: "MCDT@1235"
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data.access_token || response.data.token || response.data;
    } catch (error) {
      console.error('Error fetching DVCLT token:', error);
      return null;
    }
  };

  const handleJudicialSync = async (index: number) => {
    const confirmed = window.confirm(
      "Xác nhận đồng bộ sang hộ tịch?"
    );

    if (!confirmed) return;

    // Set loading state
    setButtonLoading('sync', index, true);

    const judicialItem = judicialData[index];

    try {
      const token = await fetchToken();
      const civilStatusType = selectedCivilStatusType[index];

      if (!civilStatusType) {
        setMessage("Vui lòng chọn loại hộ tịch (Khai Sinh hoặc Khai Tử)");
        setIsError(true);
        setSnackbarVisible(true);
        setButtonLoading('sync', index, false);
        return;
      }
      const requestBody = {
        dossierCode: judicialItem.dossierCode,
        resource: judicialItem.resource
      };

      // Choose API endpoint based on civil status type
      let apiUrl = "";
      if (civilStatusType === "KhaiSinh") {
        apiUrl = `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b611be835bc001ff53721&showIntputSendEnable=true`;
      } else if (civilStatusType === "KhaiTu") {
        apiUrl = `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b7664e835bc001ff53723&showIntputSendEnable=true`;
      } else if (civilStatusType === "KetHon") {
        apiUrl = `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=6736c5a57f2a3a001fffc7c2-685b9e38e835bc001ff53727&showIntputSendEnable=true`;
      } else if (civilStatusType === "XacNhanTinhTrangHonNhan") {
        apiUrl = `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b9cdbe835bc001ff53725&showIntputSendEnable=true`;
      }

      console.log("Judicial Sync Request Body:", JSON.stringify(requestBody, null, 2));
      console.log("API URL:", apiUrl);

      const response = await axios.post(
        apiUrl,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Store the response for display and potential direct call
      setJudicialResponses(prev => ({ ...prev, [index]: response.data }));

      if (response.status >= 200 && response.status < 300) {
        setMessage(`Đồng bộ sang hộ tịch thành công cho hồ sơ ${judicialItem.dossierCode}! Bây giờ bạn có thể chọn gateway để gọi tiếp.`);
        setIsError(false);

        // Show success response in modal
        showResponseInModal(`Kết quả đồng bộ hồ sơ ${judicialItem.dossierCode}`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
          dossierCode: judicialItem.dossierCode,
          civilStatusType: civilStatusType
        });
      } else {
        setMessage(`Đồng bộ thất bại cho hồ sơ ${judicialItem.dossierCode}: ${response.statusText}`);
        setIsError(true);

        // Show error response in modal
        showResponseInModal(`Lỗi đồng bộ hồ sơ ${judicialItem.dossierCode}`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
          dossierCode: judicialItem.dossierCode
        });
      }

    } catch (error: any) {
      console.error("Judicial sync error:", error);
      const errorResponse = error.response?.data;
      if (errorResponse) {
        setJudicialResponses(prev => ({ ...prev, [index]: errorResponse }));
      }
      setMessage(`Đồng bộ thất bại cho hồ sơ ${judicialItem.dossierCode}: ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi đồng bộ hồ sơ ${judicialItem.dossierCode}`, {
        error: error.message,
        errorData: error.response?.data,
        status: error.response?.status || 500,
        stack: error.stack,
        dossierCode: judicialItem.dossierCode
      });
    } finally {
      // Clear loading state
      setButtonLoading('sync', index, false);
      setSnackbarVisible(true);
    }
  };

  const handleDirectGatewayCall = async (index: number, judicialResponse: any) => {
    const gateway = selectedGateway[index];
    const inputSend = judicialResponse.inputSend;

    if (!inputSend) {
      setMessage("Không tìm thấy dữ liệu inputSend để gọi trực tiếp");
      setIsError(true);
      setSnackbarVisible(true);
      return;
    }

    // Set loading state
    setLoading(true);
    setMessage(`Đang gọi ${gateway}...`);
    setIsError(false);
    setSnackbarVisible(true);

    try {
      // Get appropriate token
      let token = null;
      let apiUrl = "";

      setMessage(`Đang lấy token từ ${gateway}...`);

      if (gateway === "LGSP") {
        token = await fetchLGSPToken();
        apiUrl = "https://am.quangngai.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT";
      } else if (gateway === "NGSP") {
        try {
          token = await fetchNGSPToken();
          apiUrl = "https://api.ngsp.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT";
        } catch (networkError: any) {
          handleNGSPNetworkError(networkError);
          return;
        }
      }

      if (!token) {
        setMessage(`Không thể lấy token từ ${gateway}`);
        setIsError(true);
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      setMessage(`Đang gọi API ${gateway}...`);

      // Prepare request body from inputSend
      const directRequestBody = {
        maHoSoMCDT: inputSend.maHoSoMCDT,
        maHoSoLT: inputSend.maHoSoLT,
        data: inputSend.data,
        module: inputSend.module,
        ngayTiepNhan: inputSend.ngayTiepNhan,
        maDonVi: inputSend.maDonVi,
        fileDinhKem: inputSend.fileDinhKem || []
      };

      console.log(`Direct ${gateway} Call Request:`, JSON.stringify(directRequestBody, null, 2));

      let response;
      if (gateway === "NGSP") {
        // Use helper function for NGSP with HTTPS/HTTP fallback
        response = await callNGSPAPILocal(token, directRequestBody);
      } else {
        // Standard call for LGSP and others
        response = await axios.post(apiUrl, directRequestBody, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }

      // Update the response to show direct call result
      const updatedResponse = {
        ...judicialResponse,
        directCallResult: {
          gateway,
          response: response.data,
          status: response.status
        }
      };

      setJudicialResponses(prev => ({ ...prev, [index]: updatedResponse }));
      setMessage(`Gọi trực tiếp ${gateway} thành công!`);
      setIsError(false);

      // Show response in modal
      showResponseInModal(`Kết quả gọi trực tiếp ${gateway}`, {
        gateway,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        request: directRequestBody,
        url: apiUrl
      });

    } catch (error: any) {
      console.error(`Direct ${gateway} call error:`, error);

      // Update response with error
      const errorResult = {
        gateway,
        error: error.response?.data || error.message,
        status: error.response?.status || 500
      };

      setJudicialResponses(prev => ({
        ...prev,
        [index]: {
          ...prev[index],
          directCallResult: errorResult
        }
      }));

      setMessage(`Gọi trực tiếp ${gateway} thất bại: ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi gọi trực tiếp ${gateway}`, {
        gateway,
        error: error.message,
        errorData: error.response?.data,
        status: error.response?.status || 500,
        stack: error.stack
      });
    } finally {
      setLoading(false);
    }

    setSnackbarVisible(true);
  };

  const handleManualDirectCall = async (index: number) => {
    const judicialResponse = judicialResponses[index];
    if (!judicialResponse) {
      setMessage("Chưa có response để gọi trực tiếp");
      setIsError(true);
      setSnackbarVisible(true);
      return;
    }

    // Set loading state for this specific button
    setButtonLoading('directCall', index, true);

    try {
      await handleDirectGatewayCall(index, judicialResponse);
    } finally {
      setButtonLoading('directCall', index, false);
    }
  };

  const handleDirectDVCLTCall = async (index: number) => {
    const log = logResults[index];

    // Prepare request body outside try block for error handling
    let requestBody = { ...log.requestBody };

    try {
      // Get DVCLT token
      const token = await fetchDVCLTToken();
      if (!token) {
        setMessage("Không thể lấy token từ DVCLT");
        setIsError(true);
        setSnackbarVisible(true);
        return;
      }

      // Override with selected status and note if available
      if (selectedStatus[index] !== undefined) {
        requestBody.trangThai = selectedStatus[index];
      }

      if (selectedNote[index] !== undefined) {
        requestBody.ghiChu = selectedNote[index];
      }

      // Map additional fields
      if (requestBody.coQuanXuLy !== undefined) {
        requestBody.maDonVi = requestBody.coQuanXuLy;
      }

      requestBody.maTinh = 51; // Default value
      requestBody.module = module; // Selected module (LTKT or LTKS)

      if (requestBody.soHoSoLT !== undefined) {
        requestBody.maHoSoLT = requestBody.soHoSoLT;
      }

      if (requestBody.maHoSo !== undefined) {
        requestBody.maHoSoMCDT = requestBody.maHoSo;
      }

      const apiUrl = 'http://***********:8080/VXPAdapter/RestService/forward/CapNhatTrangThai/?providerurl=http://lienthong.dichvucong.gov.vn:8135&dstcode=VN:GOV:G01:CSDLDC';

      console.log("Direct DVCLT Call Request Body:", JSON.stringify(requestBody, null, 2));

      const response = await axios.post(apiUrl, requestBody, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      setMessage(`Gọi trực tiếp DVCLT thành công cho log ${index + 1}!`);
      setIsError(false);

      // Show response in modal
      showResponseInModal(`Kết quả gọi trực tiếp DVCLT Log ${index + 1}`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        request: requestBody,
        url: apiUrl
      });

    } catch (error: any) {
      console.error("Direct DVCLT call error:", error);
      setMessage(`Gọi trực tiếp DVCLT thất bại cho log ${index + 1}: ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi gọi trực tiếp DVCLT Log ${index + 1}`, {
        error: error.message,
        errorData: error.response?.data,
        status: error.response?.status || 500,
        stack: error.stack,
        request: requestBody
      });
    }

    setSnackbarVisible(true);
  };

  const fetchToken = async (): Promise<string> => {
    const accessToken = localStorage.getItem("access_token");
    if (!accessToken) {
      console.error("Không tìm thấy accessToken trong localStorage");
      return "";
    }
    return accessToken;
  };

  const handleLogCheck = async () => {
    if (option === "Đồng bộ sang hộ tịch") {
      await handleJudicialDataFetch();
    } else if (option === "Lấy trạng thái từ hộ tịch") {
      await handleSyncStatusDataFetch();
    } else if (option === "Chuyển đổi mã hồ sơ") {
      await handleCodeConversionDataFetch();
    } else {
      await handleDVCLTLogFetch();
    }
  };

  const handleDVCLTLogFetch = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const logResults: LogResult[] = [];
    setLoading(true);
    setIsError(false);
    setMessage("");
    setLogResults([]);
    setSelectedStatus({});
    setSelectedNote({});

    try {
      for (const record of records) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/ad/api/lienthongDVCLT/getLog?nationCode=${record}`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            if (data.content && Array.isArray(data.content)) {
              logResults.push(...data.content.map((item: any) => ({
                id: item.id,
                api: item.api,
                responseBody: item.responseBody,
                requestBody: item.requestBody,
                nationCode: record
              })));
            }
          }
        } catch (error) {
          console.error(`Failed to fetch log for record ${record}:`, error);
        }
      }

      setMessage("Lấy dữ liệu thành công!");
      setLogResults(logResults);
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleJudicialDataFetch = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const judicialResults: any[] = [];
    setLoading(true);
    setIsError(false);
    setMessage("");
    setJudicialData([]);
    setShowCivilStatusOptions(false);

    try {
      for (const record of records) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/pa/api-integration/--data?code=${record}&fields=commonEForm,detailEForm,dossierFormFile,dossierFee,currentTask`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            judicialResults.push({
              dossierCode: record,
              resource: data
            });
          }
        } catch (error) {
          console.error(`Failed to fetch judicial data for record ${record}:`, error);
        }
      }

      setMessage("Lấy dữ liệu hộ tịch thành công!");
      setJudicialData(judicialResults);
      setShowCivilStatusOptions(true);
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to determine civil status type from procedure code
  const determineCivilStatusType = (procedureCode: string): string | null => {
    if (procedureCode === "2.000986.000.00.00.H48") {
      return "KhaiSinh";
    } else if (procedureCode === "2.002622.H48" || procedureCode === "1.011537.000.00.00.H48") {
      return "KhaiTu";
    } else if (procedureCode === "1.001193.000.00.00.H48") {
      return "KhaiSinh";
    } else if (procedureCode === "1.000894.000.00.00.H48") {
      return "KetHon";
    } else if (procedureCode === "1.004873.000.00.00.H48") {
      return "XacNhanTinhTrangHonNhan";
    }
    return null;
  };

  // Helper function to get API URL based on civil status type
  const getApiUrlForCivilStatusType = (civilStatusType: string): string => {
    switch (civilStatusType) {
      case "KhaiSinh":
        return `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b611be835bc001ff53721&showIntputSendEnable=true`;
      case "KhaiTu":
        return `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b7664e835bc001ff53723&showIntputSendEnable=true`;
      case "KetHon":
        return `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=6736c5a57f2a3a001fffc7c2-685b9e38e835bc001ff53727&showIntputSendEnable=true`;
      case "XacNhanTinhTrangHonNhan":
        return `${API_BASE_URL}/ad/service/judicial-civil-status/--new-dossier?option=656ea6ff0acf4b001ecb4495-685b9cdbe835bc001ff53725&showIntputSendEnable=true`;
      default:
        return "";
    }
  };

  const handleFetchAndAutoSync = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const judicialResults: any[] = [];
    const syncResults: any[] = [];

    setFetchAndSyncLoading(true);
    setIsError(false);
    setMessage("Đang lấy dữ liệu và tự động đồng bộ...");
    setJudicialData([]);
    setShowCivilStatusOptions(false);
    setJudicialResponses({});

    try {
      // Step 1: Fetch data for all records
      setMessage("Đang lấy dữ liệu...");
      for (const record of records) {
        try {
          const response = await fetch(
            `${API_BASE_URL}/pa/api-integration/--data?code=${record}&fields=commonEForm,detailEForm,dossierFormFile,dossierFee,currentTask`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            judicialResults.push({
              dossierCode: record,
              resource: data
            });
          }
        } catch (error) {
          console.error(`Failed to fetch judicial data for record ${record}:`, error);
          judicialResults.push({
            dossierCode: record,
            resource: null,
            error: `Lỗi lấy dữ liệu: ${error}`
          });
        }
      }

      // Step 2: Auto sync each record
      setMessage("Đang tự động đồng bộ...");
      for (let i = 0; i < judicialResults.length; i++) {
        const judicialItem = judicialResults[i];

        if (!judicialItem.resource) {
          syncResults.push({
            dossierCode: judicialItem.dossierCode,
            success: false,
            error: "Không có dữ liệu resource để đồng bộ",
            civilStatusType: null
          });
          continue;
        }

        try {
          // Determine civil status type from procedure code
          const procedureCode = judicialItem.resource?.procedure?.code;
          const civilStatusType = determineCivilStatusType(procedureCode);

          if (!civilStatusType) {
            syncResults.push({
              dossierCode: judicialItem.dossierCode,
              success: false,
              error: "Không tìm thấy thủ tục đồng bộ",
              procedureCode: procedureCode,
              civilStatusType: null
            });
            continue;
          }

          setMessage(`Đang đồng bộ ${judicialItem.dossierCode} (${civilStatusType})...`);

          const requestBody = {
            dossierCode: judicialItem.dossierCode,
            resource: judicialItem.resource
          };

          const apiUrl = getApiUrlForCivilStatusType(civilStatusType);

          console.log("Auto Sync Request Body:", JSON.stringify(requestBody, null, 2));
          console.log("API URL:", apiUrl);

          const response = await axios.post(
            apiUrl,
            requestBody,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );

          syncResults.push({
            dossierCode: judicialItem.dossierCode,
            success: true,
            response: response.data,
            status: response.status,
            statusText: response.statusText,
            civilStatusType: civilStatusType,
            procedureCode: procedureCode
          });

          // Store the response for display
          setJudicialResponses(prev => ({ ...prev, [i]: response.data }));

        } catch (error: any) {
          console.error(`Auto sync error for ${judicialItem.dossierCode}:`, error);
          const errorResponse = error.response?.data;

          syncResults.push({
            dossierCode: judicialItem.dossierCode,
            success: false,
            error: error.message,
            errorData: errorResponse,
            status: error.response?.status || 500,
            civilStatusType: determineCivilStatusType(judicialItem.resource?.procedure?.code),
            procedureCode: judicialItem.resource?.procedure?.code
          });

          if (errorResponse) {
            setJudicialResponses(prev => ({ ...prev, [i]: errorResponse }));
          }
        }
      }

      // Update UI with results
      setJudicialData(judicialResults);
      setShowCivilStatusOptions(true);

      // Set civil status types automatically
      const autoSelectedTypes: { [key: number]: string } = {};
      judicialResults.forEach((item, index) => {
        const procedureCode = item.resource?.procedure?.code;
        const civilStatusType = determineCivilStatusType(procedureCode);
        if (civilStatusType) {
          autoSelectedTypes[index] = civilStatusType;
        }
      });
      setSelectedCivilStatusType(autoSelectedTypes);

      const successCount = syncResults.filter(r => r.success).length;
      const errorCount = syncResults.filter(r => !r.success).length;

      setMessage(`Hoàn tất! Thành công: ${successCount}, Lỗi: ${errorCount}`);
      setIsError(errorCount > 0);

      // Show results in modal
      showResponseInModal(`Kết quả lấy dữ liệu và đồng bộ tự động`, {
        totalProcessed: records.length,
        successCount: successCount,
        errorCount: errorCount,
        results: syncResults
      });

    } catch (error: any) {
      console.error("Fetch and auto sync error:", error);
      setMessage(`Lỗi: ${error.message}`);
      setIsError(true);

      showResponseInModal(`Lỗi lấy dữ liệu và đồng bộ tự động`, {
        error: error.message,
        stack: error.stack
      });
    } finally {
      setFetchAndSyncLoading(false);
      setSnackbarVisible(true);
    }
  };

  const handleSyncStatusDataFetch = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    const token = await fetchToken();
    const syncResults: any[] = [];
    setLoading(true);
    setIsError(false);
    setMessage("");
    setSyncStatusData([]);
    setShowSyncStatusOptions(false);

    try {
      for (const record of records) {
        try {
          const response = await axios.post(
            `${API_BASE_URL}/pa/judicial-civil-status/--sync-dossiers?code=${record}&showInputSend=keytest_8957`,
            {},
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );

          if (response.status >= 200 && response.status < 300) {
            syncResults.push({
              dossierCode: record,
              responseData: response.data
            });
          }
        } catch (error: any) {
          console.error(`Failed to fetch sync status for record ${record}:`, error);
          // Still add the record with error info
          syncResults.push({
            dossierCode: record,
            responseData: null,
            error: error.response?.data || error.message
          });
        }
      }

      setMessage("Lấy trạng thái từ hộ tịch thành công!");
      setSyncStatusData(syncResults);
      setShowSyncStatusOptions(true);
    } catch (error: any) {
      setMessage(`API call error: ${error.message}`);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async (index: number) => {
    const confirmed = window.confirm(
      "Xác nhận gửi lại yêu cầu này?"
    );

    if (!confirmed) return;

    // Set loading state
    setButtonLoading('resend', index, true);

    try {
      // If direct DVCLT is enabled, call directly to DVCLT
      if (enableDirectDVCLT[index]) {
        await handleDirectDVCLTCall(index);
        return;
      }

      const token = await fetchToken();
      const log = logResults[index];
      // requestBody is already an object, no need to parse
      let requestBody = { ...log.requestBody };

      // Override with selected status and note if available
      if (selectedStatus[index] !== undefined) {
        requestBody.trangThai = selectedStatus[index];
      }

      if (selectedNote[index] !== undefined) {
        requestBody.ghiChu = selectedNote[index];
      }

      // Map additional fields
      if (requestBody.coQuanXuLy !== undefined) {
        requestBody.maDonVi = requestBody.coQuanXuLy;
      }

      requestBody.maTinh = 51; // Default value
      requestBody.module = module; // Selected module (LTKT or LTKS)

      if (requestBody.soHoSoLT !== undefined) {
        requestBody.maHoSoLT = requestBody.soHoSoLT;
      }

      if (requestBody.maHoSo !== undefined) {
        requestBody.maHoSoMCDT = requestBody.maHoSo;
      }

      // Transform path API - remove "POST " prefix and create full URL
      let pathApi = log.api;
      if (pathApi.startsWith("POST ")) {
        pathApi = pathApi.substring(5);
      }

      const fullUrl = `${API_BASE_URL}/ad${pathApi}`;

      console.log("Request Body:", JSON.stringify(requestBody, null, 2));

      const response = await axios.post(
        fullUrl,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        const logCode = logResults[index]?.nationCode || 'N/A';
        setMessage(`Gửi lại thành công cho log ${index + 1} (Code: ${logCode})!`);
        setIsError(false);

        // Show response in modal
        showResponseInModal(`Kết quả gửi lại Log ${index + 1} - Code: ${logCode}`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
          request: requestBody,
          url: fullUrl
        });
      } else {
        const logCode = logResults[index]?.nationCode || 'N/A';
        setMessage(`Gửi lại thất bại cho log ${index + 1} (Code: ${logCode}): ${response.statusText}`);
        setIsError(true);

        // Show error response in modal
        showResponseInModal(`Lỗi gửi lại Log ${index + 1} - Code: ${logCode}`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
          request: requestBody,
          url: fullUrl
        });
      }

    } catch (error: any) {
      console.error("Resend error:", error);
      const logCode = logResults[index]?.nationCode || 'N/A';
      setMessage(`Gửi lại thất bại cho log ${index + 1} (Code: ${logCode}): ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi gửi lại Log ${index + 1} - Code: ${logCode}`, {
        error: error.message,
        stack: error.stack,
        request: logResults[index]?.requestBody
      });
    } finally {
      // Clear loading state
      setButtonLoading('resend', index, false);
      setSnackbarVisible(true);
    }
  };

  const handleDirectStatusGatewayCall = async (index: number) => {
    const syncItem = syncStatusData[index];
    const gateway = selectedStatusGateway[index];
    const noiDangKy = noiDangKyInput[index];

    if (!noiDangKy) {
      setMessage("Vui lòng nhập nơi đăng ký");
      setIsError(true);
      setSnackbarVisible(true);
      return;
    }

    // Set loading state for this specific button
    setButtonLoading('statusCall', index, true);
    setMessage(`Đang gọi ${gateway}...`);
    setIsError(false);
    setSnackbarVisible(true);

    try {
      // Get appropriate token
      let token = null;
      let apiUrl = "";

      setMessage(`Đang lấy token từ ${gateway}...`);

      if (gateway === "LGSP") {
        token = await fetchLGSPToken();
        apiUrl = "https://am.quangngai.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT";
      } else if (gateway === "NGSP") {
        try {
          token = await fetchNGSPToken();
          apiUrl = "https://api.ngsp.gov.vn/Lienthonghotich/1.0/nhanHoSoDKHT";
        } catch (networkError: any) {
          handleNGSPNetworkError(networkError);
          return;
        }
      }

      if (!token) {
        setMessage(`Không thể lấy token từ ${gateway}`);
        setIsError(true);
        setSnackbarVisible(true);
        setLoading(false);
        return;
      }

      setMessage(`Đang gọi API ${gateway}...`);

      // Prepare request body
      const requestBody = {
        maTinh: "51",
        maDinhDanhHoSo: [syncItem.dossierCode],
        noiDangKy: noiDangKy,
        module: module
      };

      console.log(`Direct Status ${gateway} Call Request:`, JSON.stringify(requestBody, null, 2));

      let response;
      if (gateway === "NGSP") {
        // Use helper function for NGSP with HTTPS/HTTP fallback
        response = await callNGSPAPILocal(token, requestBody);
      } else {
        // Standard call for LGSP and others
        response = await axios.post(apiUrl, requestBody, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }

      // Update the response to show direct call result
      const updatedResponse = {
        ...syncItem,
        directStatusCallResult: {
          gateway,
          response: response.data,
          status: response.status
        }
      };

      setSyncStatusResponses(prev => ({ ...prev, [index]: updatedResponse }));
      setMessage(`Gọi trực tiếp ${gateway} để lấy trạng thái thành công!`);
      setIsError(false);

      // Show response in modal
      showResponseInModal(`Kết quả trạng thái ${gateway}`, {
        gateway,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        request: requestBody,
        url: apiUrl
      });

    } catch (error: any) {
      console.error(`Direct status ${gateway} call error:`, error);

      // Update response with error
      const errorResult = {
        gateway,
        error: error.response?.data || error.message,
        status: error.response?.status || 500
      };

      setSyncStatusResponses(prev => ({
        ...prev,
        [index]: {
          ...syncStatusData[index],
          directStatusCallResult: errorResult
        }
      }));

      setMessage(`Gọi trực tiếp ${gateway} để lấy trạng thái thất bại: ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi trạng thái ${gateway}`, {
        gateway,
        error: error.message,
        errorData: error.response?.data,
        status: error.response?.status || 500,
        stack: error.stack
      });
    } finally {
      setButtonLoading('statusCall', index, false);
    }

    setSnackbarVisible(true);
  };

  // Handlers for code conversion feature
  const handleGlobalConversionTypeChange = (type: string) => {
    setGlobalConversionType(type);
  };

  const handleJudicialResendAll = async () => {
    // Parse input text to get list of dossier codes
    const dossierCodes = inputText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    if (dossierCodes.length === 0) {
      setMessage("Vui lòng nhập danh sách mã hồ sơ");
      setIsError(true);
      setSnackbarVisible(true);
      return;
    }

    const confirmed = window.confirm(
      `Xác nhận gọi lại HTTP cho ${dossierCodes.length} hồ sơ?`
    );

    if (!confirmed) return;

    setJudicialResendLoading(true);
    setMessage("Đang gọi lại HTTP cho tất cả hồ sơ...");
    setIsError(false);
    setSnackbarVisible(true);

    try {
      const token = await fetchToken();
      if (!token) {
        setMessage("Không thể lấy token để gọi API");
        setIsError(true);
        setSnackbarVisible(true);
        return;
      }

      const results: any[] = [];

      for (let i = 0; i < dossierCodes.length; i++) {
        const dossierCode = dossierCodes[i];
        try {
          setMessage(`Đang gọi lại HTTP cho hồ sơ ${dossierCode} (${i + 1}/${dossierCodes.length})...`);

          const response = await axios.post(
            `${API_BASE_URL}/pa/judicial-civil-status/--resend?code=${dossierCode}`,
            {}, // Empty body for POST request
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );

          results.push({
            dossierCode: dossierCode,
            status: response.status,
            statusText: response.statusText,
            data: response.data,
            success: true
          });

        } catch (error: any) {
          console.error(`Error resending ${dossierCode}:`, error);
          results.push({
            dossierCode: dossierCode,
            status: error.response?.status || 500,
            statusText: error.response?.statusText || 'Error',
            data: error.response?.data || error.message,
            success: false,
            error: error.message
          });
        }
      }

      setMessage(`Hoàn tất gọi lại HTTP! Đã xử lý ${dossierCodes.length} hồ sơ.`);
      setIsError(false);

      // Show results in modal
      showResponseInModal(`Kết quả gọi lại HTTP cho ${dossierCodes.length} hồ sơ`, {
        totalProcessed: dossierCodes.length,
        successCount: results.filter(r => r.success).length,
        errorCount: results.filter(r => !r.success).length,
        results: results
      });

    } catch (error: any) {
      console.error("Judicial resend all error:", error);
      setMessage(`Lỗi gọi lại HTTP: ${error.message}`);
      setIsError(true);

      showResponseInModal(`Lỗi gọi lại HTTP`, {
        error: error.message,
        stack: error.stack
      });
    } finally {
      setJudicialResendLoading(false);
      setSnackbarVisible(true);
    }
  };

  const handleBulkCodeConversion = async () => {
    if (!globalConversionType) {
      setMessage("Vui lòng chọn loại chuyển đổi");
      setIsError(true);
      setSnackbarVisible(true);
      return;
    }

    // Set loading state
    setConversionLoading(true);
    setMessage("Đang chuyển đổi mã hồ sơ...");
    setIsError(false);
    setSnackbarVisible(true);

    try {
      const token = await fetchToken();
      const results: string[] = [];
      let resultField = "";

      if (globalConversionType === "MC_TO_QG") {
        resultField = "nationCode";
      } else if (globalConversionType === "QG_TO_MC") {
        resultField = "code";
      }

      for (const dossierCode of codeConversionData) {
        try {
          let apiUrl = "";
          if (globalConversionType === "MC_TO_QG") {
            // Đổi mã MC thành mã QG
            apiUrl = `${API_BASE_URL}/pa/dossier/${dossierCode}/--by-code`;
          } else if (globalConversionType === "QG_TO_MC") {
            // Đổi mã QG thành mã MC
            apiUrl = `${API_BASE_URL}/pa/dossier/${dossierCode}/--by-nation-code`;
          }

          console.log("Code Conversion Request:", { dossierCode, globalConversionType, apiUrl });

          const response = await fetch(apiUrl, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            const convertedCode = data[resultField];

            if (convertedCode) {
              results.push(convertedCode);
            } else {
              results.push(`${dossierCode} - Không tìm thấy`);
            }
          } else {
            results.push(`${dossierCode} - Lỗi: ${response.statusText}`);
          }
        } catch (error: any) {
          console.error(`Error converting ${dossierCode}:`, error);
          results.push(`${dossierCode} - Lỗi: ${error.message}`);
        }
      }

      // Clean results by removing quotes
      const cleanResults = results.map(result => result.replace(/"/g, ''));

      setConversionResults(cleanResults);
      setMessage(`Chuyển đổi hoàn tất! Đã xử lý ${codeConversionData.length} mã hồ sơ.`);
      setIsError(false);

      // Show results in modal
      showResponseInModal(`Kết quả chuyển đổi ${globalConversionType === "MC_TO_QG" ? "MC → QG" : "QG → MC"}`, {
        conversionType: globalConversionType === "MC_TO_QG" ? "MC → QG" : "QG → MC",
        totalProcessed: codeConversionData.length,
        originalCodes: codeConversionData,
        convertedCodes: cleanResults,
        resultsList: cleanResults.join('\n')
      });

    } catch (error: any) {
      console.error("Bulk code conversion error:", error);
      setMessage(`Lỗi chuyển đổi: ${error.message}`);
      setIsError(true);

      // Show error in modal
      showResponseInModal(`Lỗi chuyển đổi mã hồ sơ`, {
        error: error.message,
        stack: error.stack,
        conversionType: globalConversionType === "MC_TO_QG" ? "MC → QG" : "QG → MC"
      });
    } finally {
      setConversionLoading(false);
      setSnackbarVisible(true);
    }
  };

  const handleCodeConversionDataFetch = async () => {
    const records = inputText.split(",").map((record) => record.trim());
    setLoading(true);
    setIsError(false);
    setMessage("");
    setCodeConversionData(records);
    setShowCodeConversionOptions(true);
    setGlobalConversionType("");
    setConversionResults([]);
    setConversionLoading(false);

    setMessage("Dữ liệu đã sẵn sàng để chuyển đổi!");
    setLoading(false);
  };

  const clearData = () => {
    setInputText("");
    setMessage("");
    setLogResults([]);
    setSnackbarVisible(false);
    setIsError(false);
    setSelectedStatus({});
    setSelectedNote({});
    setJudicialData([]);
    setShowCivilStatusOptions(false);
    setSelectedCivilStatusType({});
    setJudicialResponses({});
    setEnableDirectCall({});
    setSelectedGateway({});
    setEditableResponses({});
    setIsEditingResponse({});
    setEditableResources({});
    setIsEditingResource({});
    setEnableDirectDVCLT({});
    // Clear sync status states
    setSyncStatusData([]);
    setShowSyncStatusOptions(false);
    setSyncStatusResponses({});
    setEnableDirectStatusCall({});
    setSelectedStatusGateway({});
    setNoiDangKyInput({});
    // Clear network warning states
    setShowNetworkWarning(false);
    setNetworkErrorMessage("");
    // Clear code conversion states
    setCodeConversionData([]);
    setShowCodeConversionOptions(false);
    setGlobalConversionType("");
    setConversionResults([]);
    setConversionLoading(false);
  };

  return (
    <div className="form-container">
      <h2>DVCLT &lt;=&gt; Một cửa bị thông &lt;=&gt; Hộ tịch</h2>

      <div className="input-group">
        <label htmlFor="dossier-codes">Danh sách mã hồ sơ:</label>
        <TextArea
          value={inputText}
          onChange={handleChangeText}
          placeholder="Nhập danh sách mã hồ sơ, cách nhau bằng dấu phẩy..."
        />
      </div>

      <div className="input-group">
        <label htmlFor="action-select">Hành động:</label>
        <select
          id="action-select"
          value={option}
          onChange={handleChangeOption}
        >
          <option value="Cập nhật trạng thái sang DVCLT">Cập nhật trạng thái sang DVCLT</option>
          <option value="Đồng bộ sang hộ tịch">Đồng bộ sang hộ tịch</option>
          <option value="Lấy trạng thái từ hộ tịch">Lấy trạng thái từ hộ tịch</option>
          <option value="Chuyển đổi mã hồ sơ">Chuyển đổi mã hồ sơ</option>
        </select>
      </div>

      {option === "Cập nhật trạng thái sang DVCLT" && (
        <div className="input-group">
          <label htmlFor="module-select">Module:</label>
          <select
            id="module-select"
            value={module}
            onChange={handleChangeModule}
          >
            <option value="LTKT">LTKT</option>
            <option value="LTKS">LTKS</option>
            <option value="KS">KS</option>
          </select>
        </div>
      )}

      <div className="button-group">
        <button
          onClick={handleLogCheck}
          disabled={loading || !inputText.trim()}
          className="btn-primary"
        >
          <FaClipboardList />
          {loading ?
            (option === "Đồng bộ sang hộ tịch" ? "Đang lấy dữ liệu..." :
              option === "Lấy trạng thái từ hộ tịch" ? "Đang lấy trạng thái..." :
                option === "Chuyển đổi mã hồ sơ" ? "Đang chuẩn bị..." :
                  "Đang lấy log...") :
            (option === "Đồng bộ sang hộ tịch" ? "Lấy dữ liệu" :
              option === "Lấy trạng thái từ hộ tịch" ? "Lấy trạng thái" :
                option === "Chuyển đổi mã hồ sơ" ? "Chuẩn bị chuyển đổi" :
                  "Lấy log")
          }
        </button>

        <button onClick={clearData} className="btn-secondary">
          <FaTrash />
          Xóa dữ liệu
        </button>

        {option === "Đồng bộ sang hộ tịch" && (
          <>
            <button
              onClick={handleJudicialResendAll}
              disabled={judicialResendLoading || !inputText.trim()}
              className="btn-resend-all"
            >
              <FaReply />
              {judicialResendLoading ? "Đang gọi lại..." : "Gọi lại HTTP"}
            </button>
            <button
              onClick={handleFetchAndAutoSync}
              disabled={fetchAndSyncLoading || !inputText.trim()}
              className="btn-fetch-and-sync"
            >
              <FaClipboardList />
              {fetchAndSyncLoading ? "Đang xử lý..." : "Lấy dữ liệu và gọi lại"}
            </button>
          </>
        )}
      </div>

      {message && (
        <MessageDisplay message={message} isError={isError} />
      )}

      {showNetworkWarning && (
        <div className="network-warning" style={{
          background: 'linear-gradient(135deg, #fef3c7, #fbbf24)',
          border: '2px solid #f59e0b',
          borderRadius: '8px',
          padding: '1rem',
          margin: '1rem 0',
          color: '#92400e'
        }}>
          <h4 style={{ margin: '0 0 0.5rem 0', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            ⚠️ Lỗi kết nối NGSP
          </h4>
          <p style={{ margin: '0 0 1rem 0' }}>{networkErrorMessage}</p>
          <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
            <strong>Giải pháp SSL Certificate:</strong>
            <ul style={{ margin: '0', paddingLeft: '1rem' }}>
              <li><strong>Chrome:</strong> Vào <code>chrome://flags/#allow-insecure-localhost</code> → Enable</li>
              <li><strong>Chrome Args:</strong> Chạy với <code>--ignore-certificate-errors --ignore-ssl-errors</code></li>
              <li><strong>Navigate directly:</strong> Mở <a href="https://api.ngsp.gov.vn/token" target="_blank">https://api.ngsp.gov.vn/token</a> → Accept risk</li>
              <li><strong>Development:</strong> Thêm domain vào browser trusted sites</li>
              <li><strong>Alternative:</strong> Sử dụng LGSP thay thế</li>
            </ul>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', marginTop: '1rem', flexWrap: 'wrap' }}>
            <button
              onClick={handleAcceptNGSPCertificate}
              style={{
                background: '#059669',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🔒 Accept SSL Certificate
            </button>
            <button
              onClick={() => setShowNetworkWarning(false)}
              style={{
                background: '#dc2626',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Đóng thông báo
            </button>
          </div>
        </div>
      )}

      {logResults.length > 0 && (
        <div className="log-results">
          <h3>Kết quả log ({logResults.length} bản ghi):</h3>
          {logResults.map((log, index) => (
            <div key={index} className="log-item">
              <div className="log-header">
                <h4>
                  Log {index + 1} - Code: {log.nationCode || 'N/A'}
                </h4>
                <button
                  onClick={() => handleResend(index)}
                  className="btn-resend"
                  title="Gọi lại"
                  disabled={resendLoading[index]}
                >
                  {resendLoading[index] ? (
                    <>
                      <div className="spinner"></div>
                      Đang gửi...
                    </>
                  ) : (
                    <>
                      <FaReply />
                      Gọi lại
                    </>
                  )}
                </button>
              </div>

              <div className="log-content">
                <div className="log-field">
                  <label>Path API:</label>
                  <div className="log-value">{log.api}</div>
                </div>

                <div className="resend-controls">
                  <div className="control-group">
                    <label>Trạng thái mới:</label>
                    <select
                      value={selectedStatus[index] || log.requestBody?.trangThai || ""}
                      onChange={(e) => handleStatusChange(index, parseInt(e.target.value))}
                    >
                      <option value="">-- Chọn trạng thái --</option>
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.value} - {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="control-group">
                    <label>Ghi chú mới:</label>
                    <textarea
                      value={selectedNote[index] || log.requestBody?.ghiChu || ""}
                      onChange={(e) => handleNoteChange(index, e.target.value)}
                      placeholder="Nhập ghi chú..."
                      rows={2}
                      style={{ width: '80%', minHeight: '3rem', maxHeight: '8rem' }}
                    />
                  </div>

                  <div className="control-group">
                    <label>Gọi thẳng DVCLT:</label>
                    <input
                      type="checkbox"
                      checked={enableDirectDVCLT[index] || false}
                      onChange={(e) => handleDirectDVCLTToggle(index, e.target.checked)}
                    />
                  </div>
                </div>

                <div className="log-field">
                  <label>Response:</label>
                  <div className="log-value">
                    <pre>{JSON.stringify(log.responseBody, null, 2)}</pre>
                  </div>
                </div>

                <div className="log-field">
                  <label>Request Body:</label>
                  <div className="log-value">
                    <pre>{JSON.stringify(log.requestBody, null, 2)}</pre>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {judicialData.length > 0 && showCivilStatusOptions && (
        <div className="judicial-results">
          <h3>Dữ liệu hộ tịch ({judicialData.length} bản ghi):</h3>
          {judicialData.map((item, index) => (
            <div key={index} className="judicial-item">
              <div className="judicial-header">
                <h4>Hồ sơ: {item.dossierCode}</h4>
                <button
                  onClick={() => handleJudicialSync(index)}
                  className="btn-sync"
                  disabled={!selectedCivilStatusType[index] || syncLoading[index]}
                  title="Đồng bộ sang hộ tịch"
                >
                  {syncLoading[index] ? (
                    <>
                      <div className="spinner"></div>
                      Đang đồng bộ...
                    </>
                  ) : (
                    <>
                      <FaClipboardList />
                      Đồng bộ
                    </>
                  )}
                </button>
              </div>

              <div className="judicial-content">
                <div className="civil-status-controls">
                  <div className="control-group">
                    <label>Loại hộ tịch:</label>
                    <select
                      value={selectedCivilStatusType[index] || ""}
                      onChange={(e) => handleCivilStatusTypeChange(index, e.target.value)}
                    >
                      <option value="">-- Chọn loại hộ tịch --</option>
                      <option value="KhaiSinh">Khai Sinh</option>
                      <option value="KhaiTu">Khai Tử</option>
                      <option value="KetHon">Kết Hôn</option>
                      <option value="XacNhanTinhTrangHonNhan">Xác nhận tình trạng hôn nhân</option>
                    </select>
                  </div>

                  <div className="control-group">
                    <label>
                      <input
                        type="checkbox"
                        checked={enableDirectCall[index] || false}
                        onChange={(e) => handleDirectCallToggle(index, e.target.checked)}
                      />
                      {" "}Gọi thẳng trục
                    </label>
                    {enableDirectCall[index] && (
                      <select
                        value={selectedGateway[index] || "LGSP"}
                        onChange={(e) => handleGatewayChange(index, e.target.value)}
                      >
                        <option value="LGSP">LGSP</option>
                        <option value="NGSP">NGSP</option>
                      </select>
                    )}
                  </div>
                </div>

                {judicialResponses[index] && (
                  <div className="response-section">
                    <div className="response-header">
                      <h5>Kết quả đồng bộ:</h5>
                      <div className="response-actions">
                        <button
                          onClick={() => isEditingResponse[index] ? handleCancelEditResponse(index) : handleToggleEditResponse(index)}
                          className="btn-edit"
                          style={{
                            backgroundColor: isEditingResponse[index] ? "#dc3545" : "#28a745",
                            color: "white",
                            padding: "5px 10px",
                            border: "none",
                            borderRadius: "4px",
                            marginRight: "10px",
                            cursor: "pointer"
                          }}
                        >
                          {isEditingResponse[index] ? "Hủy" : "Sửa Response"}
                        </button>

                        {isEditingResponse[index] && (
                          <button
                            onClick={() => handleSaveEditedResponse(index)}
                            className="btn-save"
                            style={{
                              backgroundColor: "#007bff",
                              color: "white",
                              padding: "5px 10px",
                              border: "none",
                              borderRadius: "4px",
                              marginRight: "10px",
                              cursor: "pointer"
                            }}
                          >
                            Lưu
                          </button>
                        )}

                        {enableDirectCall[index] && judicialResponses[index].inputSend && (
                          <button
                            onClick={() => handleManualDirectCall(index)}
                            className="btn-direct-call"
                            disabled={!selectedGateway[index] || isEditingResponse[index] || directCallLoading[index]}
                          >
                            {directCallLoading[index] ? (
                              <>
                                <div className="spinner"></div>
                                Đang gọi...
                              </>
                            ) : (
                              `Gọi trực tiếp ${selectedGateway[index] || "LGSP"}`
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                    <div className="judicial-field">
                      <label>Response:</label>
                      <div className="judicial-value">
                        {isEditingResponse[index] ? (
                          <textarea
                            value={editableResponses[index] || ""}
                            onChange={(e) => handleUpdateEditableResponse(index, e.target.value)}
                            style={{
                              width: "100%",
                              minHeight: "200px",
                              fontFamily: "monospace",
                              fontSize: "12px",
                              padding: "10px",
                              border: "1px solid #ccc",
                              borderRadius: "4px"
                            }}
                            placeholder="JSON response data..."
                          />
                        ) : (
                          <pre>{JSON.stringify(judicialResponses[index], null, 2)}</pre>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <div className="judicial-field">
                  <div className="resource-header">
                    <label>Dữ liệu resource:</label>
                    <div className="resource-actions">
                      <button
                        onClick={() => isEditingResource[index] ? handleCancelEditResource(index) : handleToggleEditResource(index)}
                        className="btn-edit"
                        style={{
                          backgroundColor: isEditingResource[index] ? "#dc3545" : "#17a2b8",
                          color: "white",
                          padding: "5px 10px",
                          border: "none",
                          borderRadius: "4px",
                          marginRight: "10px",
                          cursor: "pointer",
                          fontSize: "12px"
                        }}
                      >
                        {isEditingResource[index] ? "Hủy" : "Sửa Resource"}
                      </button>
                      {isEditingResource[index] && (
                        <button
                          onClick={() => handleSaveEditedResource(index)}
                          style={{
                            backgroundColor: "#28a745",
                            color: "white",
                            padding: "5px 10px",
                            border: "none",
                            borderRadius: "4px",
                            marginRight: "10px",
                            cursor: "pointer",
                            fontSize: "12px"
                          }}
                        >
                          Lưu
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="judicial-value">
                    {isEditingResource[index] ? (
                      <textarea
                        value={editableResources[index] || ""}
                        onChange={(e) => handleUpdateEditableResource(index, e.target.value)}
                        style={{
                          width: "100%",
                          minHeight: "300px",
                          fontFamily: "monospace",
                          fontSize: "12px",
                          padding: "10px",
                          border: "1px solid #ccc",
                          borderRadius: "4px"
                        }}
                        placeholder="JSON resource data..."
                      />
                    ) : (
                      <pre>{JSON.stringify(item.resource, null, 2)}</pre>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {syncStatusData.length > 0 && showSyncStatusOptions && (
        <div className="sync-status-results">
          <h3>Kết quả trạng thái hộ tịch ({syncStatusData.length} bản ghi):</h3>
          {syncStatusData.map((item, index) => (
            <div key={index} className="sync-status-item">
              <div className="sync-status-header">
                <h4>Hồ sơ: {item.dossierCode}</h4>
                <button
                  onClick={() => handleDirectStatusGatewayCall(index)}
                  className="btn-status-call"
                  disabled={!enableDirectStatusCall[index] || !noiDangKyInput[index] || statusCallLoading[index]}
                  title="Gọi trực tiếp gateway để lấy trạng thái"
                >
                  {statusCallLoading[index] ? (
                    <>
                      <div className="spinner"></div>
                      Đang gọi...
                    </>
                  ) : (
                    <>
                      <FaClipboardList />
                      Gọi {selectedStatusGateway[index] || "LGSP"}
                    </>
                  )}
                </button>
              </div>

              <div className="sync-status-content">
                <div className="status-call-controls">
                  <div className="control-group">
                    <label>
                      <input
                        type="checkbox"
                        checked={enableDirectStatusCall[index] || false}
                        onChange={(e) => handleDirectStatusCallToggle(index, e.target.checked)}
                      />
                      {" "}Gọi thẳng gateway
                    </label>
                    {enableDirectStatusCall[index] && (
                      <select
                        value={selectedStatusGateway[index] || "LGSP"}
                        onChange={(e) => handleStatusGatewayChange(index, e.target.value)}
                      >
                        <option value="LGSP">LGSP</option>
                        <option value="NGSP">NGSP</option>
                      </select>
                    )}
                  </div>

                  {enableDirectStatusCall[index] && (
                    <div className="control-group">
                      <label>Nơi đăng ký:</label>
                      <input
                        type="text"
                        value={noiDangKyInput[index] || ""}
                        onChange={(e) => handleNoiDangKyChange(index, e.target.value)}
                        placeholder="Nhập mã nơi đăng ký (ví dụ: 21211)"
                      />
                    </div>
                  )}
                </div>

                {syncStatusResponses[index] && (
                  <div className="status-response-section">
                    <h5>Kết quả gọi gateway:</h5>
                    <div className="sync-status-field">
                      <label>Response:</label>
                      <div className="sync-status-value">
                        <pre>{JSON.stringify(syncStatusResponses[index], null, 2)}</pre>
                      </div>
                    </div>
                  </div>
                )}

                <div className="sync-status-field">
                  <label>Dữ liệu response từ API sync:</label>
                  <div className="sync-status-value">
                    {item.error ? (
                      <div className="error-message">
                        <strong>Error:</strong> {JSON.stringify(item.error, null, 2)}
                      </div>
                    ) : (
                      <pre>{JSON.stringify(item.responseData, null, 2)}</pre>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {codeConversionData.length > 0 && showCodeConversionOptions && (
        <div className="code-conversion-results">
          <h3>Chuyển đổi mã hồ sơ ({codeConversionData.length} bản ghi):</h3>

          <div className="conversion-controls">
            <div className="control-group">
              <label>Loại chuyển đổi:</label>
              <select
                value={globalConversionType}
                onChange={(e) => handleGlobalConversionTypeChange(e.target.value)}
              >
                <option value="">-- Chọn loại chuyển đổi --</option>
                <option value="MC_TO_QG">Đổi mã MC thành mã QG</option>
                <option value="QG_TO_MC">Đổi mã QG thành mã MC</option>
              </select>
            </div>

            <button
              onClick={handleBulkCodeConversion}
              className="btn-convert"
              disabled={!globalConversionType || conversionLoading}
              title="Chuyển đổi tất cả mã"
            >
              {conversionLoading ? (
                <>
                  <div className="spinner"></div>
                  Đang chuyển đổi...
                </>
              ) : (
                <>
                  <FaReply />
                  Chuyển đổi tất cả
                </>
              )}
            </button>
          </div>

          <div className="input-codes">
            <h4>Danh sách mã đầu vào:</h4>
            <div className="codes-list">
              {codeConversionData.map((code, index) => (
                <div key={index} className="code-item">{code}</div>
              ))}
            </div>
          </div>

          {conversionResults.length > 0 && (
            <div className="conversion-result">
              <div className="result-header">
                <h4>Kết quả chuyển đổi ({globalConversionType === "MC_TO_QG" ? "MC → QG" : "QG → MC"}):</h4>
              </div>
              <div className="result-content">
                <div className="results-list">
                  {conversionResults.map((result, index) => (
                    <div key={index} className="result-item">{result}</div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Response Modal */}
      {showResponseModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{modalTitle}</h3>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>
            <div className="modal-body">
              <pre className="response-content">
                {formatJSONWithUnquotedArrays(modalResponse)}
              </pre>
            </div>
            <div className="modal-footer">
              <button className="btn-secondary" onClick={closeModal}>Đóng</button>
              <button
                className="btn-primary"
                onClick={() => {
                  navigator.clipboard.writeText(formatJSONWithUnquotedArrays(modalResponse));
                  setMessage("Đã copy response vào clipboard!");
                  setIsError(false);
                  setSnackbarVisible(true);
                }}
              >
                Copy Response
              </button>
            </div>
          </div>
        </div>
      )}

      {snackbarVisible && (
        <Snackbar
          message={message}
          isError={isError}
          onClose={() => setSnackbarVisible(false)}
        />
      )}
    </div>
  );
};

export default FormDVCLT; 